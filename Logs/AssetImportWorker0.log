Unity Editor version:    6000.0.46f1 (fb93bc360d3a)
Branch:                  6000.0/release
Build type:              Release
Batch mode:              YES
macOS version:           Version 15.5 (Build 24F74)
Darwin version:          24.5.0
Architecture:            arm64
Running under Rosetta:   NO
Available memory:        65536 MB
Using pre-set license
Pro License: YES

COMMAND LINE ARGUMENTS:
/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/MacOS/Unity
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
/Users/<USER>/Documents/Myproject
-logFile
Logs/AssetImportWorker0.log
-srvPort
50475
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: /Users/<USER>/Documents/Myproject
/Users/<USER>/Documents/Myproject
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [8494161664]  Target information:

Player connection [8494161664]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1651265235 [EditorId] 1651265235 [Version] 1048832 [Id] OSXEditor(0,mingyundemaodeMacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8494161664]  * "[IP] ********** [Port] 0 [Flags] 2 [Guid] 1651265235 [EditorId] 1651265235 [Version] 1048832 [Id] OSXEditor(0,mingyundemaodeMacBook-Pro.local) [Debug] 1 [PackageName] OSXEditor [ProjectName] Editor" 

Player connection [8494161664] Host joined multi-casting on [***********:54997]...
Player connection [8494161664] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 0.49 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.46f1 (fb93bc360d3a)
[Subsystems] Discovering subsystems at path /Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path /Users/<USER>/Documents/Myproject/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
 preferred device: Apple M3 Max (high power)
Metal devices available: 1
0: Apple M3 Max (high power)
Using device Apple M3 Max (high power)
Initializing Metal device caps: Apple M3 Max
Initialize mono
Mono path[0] = '/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Managed'
Mono path[1] = '/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/MonoBleedingEdge/lib/mono/unityjit-macos'
Mono config path = '/Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56497
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.46f1/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/PlaybackEngines/MacStandaloneSupport/UnityEditor.OSXStandalone.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.46f1/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Register platform support module: /Applications/Unity/Hub/Editor/6000.0.46f1/PlaybackEngines/iOSSupport/UnityEditor.iOS.Extensions.dll
Registered in 0.001218 seconds.
- Loaded All Assemblies, in  0.208 seconds
[usbmuxd] Start listen thread
[usbmuxd] Listen thread started
[usbmuxd] Send listen message
Native extension for iOS target not found
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 58 ms
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.339 seconds
Domain Reload Profiling: 548ms
	BeginReloadAssembly (45ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (0ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (104ms)
		LoadAssemblies (44ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (102ms)
			TypeCache.Refresh (101ms)
				TypeCache.ScanAssembly (93ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (340ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (308ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (151ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (35ms)
			ProcessInitializeOnLoadAttributes (77ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.418 seconds
Refreshing native plugins compatible for Editor in 0.30 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.508 seconds
Domain Reload Profiling: 926ms
	BeginReloadAssembly (85ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (29ms)
	RebuildCommonClasses (25ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (278ms)
		LoadAssemblies (153ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (159ms)
			TypeCache.Refresh (128ms)
				TypeCache.ScanAssembly (115ms)
			BuildScriptInfoCaches (23ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (508ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (422ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (86ms)
			ProcessInitializeOnLoadAttributes (274ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (2ms)
Launching external process: /Applications/Unity/Hub/Editor/6000.0.46f1/Unity.app/Contents/Tools/UnityShaderCompiler
Launched and connected shader compiler UnityShaderCompiler after 0.03 seconds
Refreshing native plugins compatible for Editor in 0.38 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 204 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6042 unused Assets / (3.4 MB). Loaded Objects now: 6733.
Memory consumption went from 168.6 MB to 165.2 MB.
Total: 6.178458 ms (FindLiveObjects: 0.634125 ms CreateObjectMapping: 0.142875 ms MarkObjects: 4.244417 ms  DeleteObjects: 1.156500 ms)

========================================================================
Received Import Request.
  Time since last request: 1092.393273 seconds.
  path: Assets/Scripts/RoleControl.cs
  artifactKey: Guid(45a3e84dc89394562818e08a3a927901) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/RoleControl.cs using Guid(45a3e84dc89394562818e08a3a927901) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8eab8ca23d44ad746ca732af1760d7cc') in 0.002834375 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x353ff7000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.363 seconds
Refreshing native plugins compatible for Editor in 0.21 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.499 seconds
Domain Reload Profiling: 863ms
	BeginReloadAssembly (99ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (222ms)
		LoadAssemblies (148ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (102ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (89ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (499ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (382ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (251ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.39 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 24 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.8 MB). Loaded Objects now: 6751.
Memory consumption went from 162.1 MB to 158.3 MB.
Total: 4.927500 ms (FindLiveObjects: 0.272584 ms CreateObjectMapping: 0.133666 ms MarkObjects: 3.530209 ms  DeleteObjects: 0.990791 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fd13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.353 seconds
Refreshing native plugins compatible for Editor in 0.26 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.492 seconds
Domain Reload Profiling: 847ms
	BeginReloadAssembly (98ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (215ms)
		LoadAssemblies (137ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (104ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (493ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (378ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (246ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.29 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.4 MB). Loaded Objects now: 6754.
Memory consumption went from 160.3 MB to 156.9 MB.
Total: 5.199209 ms (FindLiveObjects: 0.282917 ms CreateObjectMapping: 0.122750 ms MarkObjects: 3.669584 ms  DeleteObjects: 1.123542 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 0.31 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6032 unused Assets / (3.2 MB). Loaded Objects now: 6754.
Memory consumption went from 149.2 MB to 146.0 MB.
Total: 4.765334 ms (FindLiveObjects: 0.266208 ms CreateObjectMapping: 0.114584 ms MarkObjects: 3.421375 ms  DeleteObjects: 0.962834 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17ffc7000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.353 seconds
Refreshing native plugins compatible for Editor in 0.20 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.502 seconds
Domain Reload Profiling: 857ms
	BeginReloadAssembly (96ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (217ms)
		LoadAssemblies (140ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (103ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (89ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (502ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (387ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (254ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 0.31 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.5 MB). Loaded Objects now: 6757.
Memory consumption went from 160.3 MB to 156.9 MB.
Total: 5.217333 ms (FindLiveObjects: 0.325166 ms CreateObjectMapping: 0.133584 ms MarkObjects: 3.588000 ms  DeleteObjects: 1.170167 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 0.47 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6032 unused Assets / (3.7 MB). Loaded Objects now: 6757.
Memory consumption went from 149.3 MB to 145.6 MB.
Total: 4.518875 ms (FindLiveObjects: 0.249542 ms CreateObjectMapping: 0.135708 ms MarkObjects: 3.261500 ms  DeleteObjects: 0.871709 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17ffc7000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.351 seconds
Refreshing native plugins compatible for Editor in 0.19 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.495 seconds
Domain Reload Profiling: 847ms
	BeginReloadAssembly (97ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (11ms)
	LoadAllAssembliesAndSetupDomain (214ms)
		LoadAssemblies (137ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (102ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (88ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (495ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (380ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (250ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.30 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.3 MB). Loaded Objects now: 6760.
Memory consumption went from 160.3 MB to 157.0 MB.
Total: 5.087667 ms (FindLiveObjects: 0.251167 ms CreateObjectMapping: 0.152541 ms MarkObjects: 3.574709 ms  DeleteObjects: 1.108917 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17ffc7000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.346 seconds
Refreshing native plugins compatible for Editor in 0.21 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.478 seconds
Domain Reload Profiling: 826ms
	BeginReloadAssembly (96ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (11ms)
	LoadAllAssembliesAndSetupDomain (210ms)
		LoadAssemblies (133ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (102ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (89ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (478ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (365ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (238ms)
			ProcessInitializeOnLoadMethodAttributes (31ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 0.29 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.3 MB). Loaded Objects now: 6763.
Memory consumption went from 160.3 MB to 157.0 MB.
Total: 5.202458 ms (FindLiveObjects: 0.302208 ms CreateObjectMapping: 0.116125 ms MarkObjects: 3.747458 ms  DeleteObjects: 1.036333 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 0.28 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6032 unused Assets / (3.4 MB). Loaded Objects now: 6763.
Memory consumption went from 149.3 MB to 145.9 MB.
Total: 4.706458 ms (FindLiveObjects: 0.287625 ms CreateObjectMapping: 0.111833 ms MarkObjects: 3.350500 ms  DeleteObjects: 0.955958 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17ffc7000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.356 seconds
Refreshing native plugins compatible for Editor in 0.22 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.500 seconds
Domain Reload Profiling: 858ms
	BeginReloadAssembly (98ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (217ms)
		LoadAssemblies (140ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (102ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (89ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (501ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (387ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (257ms)
			ProcessInitializeOnLoadMethodAttributes (30ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (7ms)
Refreshing native plugins compatible for Editor in 0.31 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.5 MB). Loaded Objects now: 6766.
Memory consumption went from 160.3 MB to 156.8 MB.
Total: 5.258500 ms (FindLiveObjects: 0.293208 ms CreateObjectMapping: 0.119542 ms MarkObjects: 3.774625 ms  DeleteObjects: 1.070833 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fd13000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.359 seconds
Refreshing native plugins compatible for Editor in 0.23 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.497 seconds
Domain Reload Profiling: 858ms
	BeginReloadAssembly (101ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (11ms)
	LoadAllAssembliesAndSetupDomain (219ms)
		LoadAssemblies (139ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (497ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (375ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (244ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.24 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.3 MB). Loaded Objects now: 6769.
Memory consumption went from 160.3 MB to 157.0 MB.
Total: 5.176750 ms (FindLiveObjects: 0.343041 ms CreateObjectMapping: 0.173292 ms MarkObjects: 3.592250 ms  DeleteObjects: 1.067875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17ffc7000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.360 seconds
Refreshing native plugins compatible for Editor in 0.20 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.480 seconds
Domain Reload Profiling: 843ms
	BeginReloadAssembly (101ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (219ms)
		LoadAssemblies (142ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (104ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (481ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (368ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (239ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.29 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.5 MB). Loaded Objects now: 6772.
Memory consumption went from 160.4 MB to 157.0 MB.
Total: 5.143125 ms (FindLiveObjects: 0.298125 ms CreateObjectMapping: 0.111458 ms MarkObjects: 3.665167 ms  DeleteObjects: 1.068166 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.356 seconds
Refreshing native plugins compatible for Editor in 0.22 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.482 seconds
Domain Reload Profiling: 839ms
	BeginReloadAssembly (100ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (215ms)
		LoadAssemblies (137ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (104ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (482ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (370ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (240ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.37 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.7 MB). Loaded Objects now: 6775.
Memory consumption went from 160.3 MB to 156.6 MB.
Total: 4.668500 ms (FindLiveObjects: 0.292958 ms CreateObjectMapping: 0.114834 ms MarkObjects: 3.214875 ms  DeleteObjects: 1.045541 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.366 seconds
Refreshing native plugins compatible for Editor in 0.22 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.503 seconds
Domain Reload Profiling: 871ms
	BeginReloadAssembly (103ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (222ms)
		LoadAssemblies (142ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (107ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (93ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (503ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (390ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (258ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.30 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.7 MB). Loaded Objects now: 6778.
Memory consumption went from 160.4 MB to 156.7 MB.
Total: 5.090458 ms (FindLiveObjects: 0.280083 ms CreateObjectMapping: 0.120167 ms MarkObjects: 3.691667 ms  DeleteObjects: 0.998125 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.362 seconds
Refreshing native plugins compatible for Editor in 0.20 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.497 seconds
Domain Reload Profiling: 861ms
	BeginReloadAssembly (103ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (218ms)
		LoadAssemblies (140ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (497ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (384ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (252ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.30 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.6 MB). Loaded Objects now: 6781.
Memory consumption went from 160.4 MB to 156.8 MB.
Total: 5.289791 ms (FindLiveObjects: 0.297542 ms CreateObjectMapping: 0.122750 ms MarkObjects: 3.780375 ms  DeleteObjects: 1.088875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 9271.512396 seconds.
  path: Assets/TextInput.inputactions
  artifactKey: Guid(8f7981e429e884f0f911bf012416ed16) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/TextInput.inputactions using Guid(8f7981e429e884f0f911bf012416ed16) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fe939ab9ec8301794b7b04c2162ca15c') in 0.012366166 seconds
Number of updated asset objects reloaded before import = 1Number of asset objects unloaded after import = 12

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.356 seconds
Refreshing native plugins compatible for Editor in 0.25 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.496 seconds
Domain Reload Profiling: 854ms
	BeginReloadAssembly (100ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (215ms)
		LoadAssemblies (138ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (104ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (496ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (381ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (250ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.31 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.5 MB). Loaded Objects now: 6785.
Memory consumption went from 160.4 MB to 156.9 MB.
Total: 5.301000 ms (FindLiveObjects: 0.311084 ms CreateObjectMapping: 0.126667 ms MarkObjects: 3.773250 ms  DeleteObjects: 1.089750 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 0.26 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6032 unused Assets / (3.5 MB). Loaded Objects now: 6785.
Memory consumption went from 149.3 MB to 145.8 MB.
Total: 4.639625 ms (FindLiveObjects: 0.286000 ms CreateObjectMapping: 0.127875 ms MarkObjects: 3.350291 ms  DeleteObjects: 0.875083 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.369 seconds
Refreshing native plugins compatible for Editor in 0.21 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.516 seconds
Domain Reload Profiling: 888ms
	BeginReloadAssembly (104ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (225ms)
		LoadAssemblies (143ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (109ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (94ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (517ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (399ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (261ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.35 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.3 MB). Loaded Objects now: 6788.
Memory consumption went from 160.3 MB to 157.0 MB.
Total: 5.307625 ms (FindLiveObjects: 0.311333 ms CreateObjectMapping: 0.120666 ms MarkObjects: 3.816209 ms  DeleteObjects: 1.059208 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.357 seconds
Refreshing native plugins compatible for Editor in 0.24 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.486 seconds
Domain Reload Profiling: 845ms
	BeginReloadAssembly (99ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (218ms)
		LoadAssemblies (139ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (486ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (372ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (245ms)
			ProcessInitializeOnLoadMethodAttributes (31ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.34 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.3 MB). Loaded Objects now: 6791.
Memory consumption went from 160.3 MB to 156.9 MB.
Total: 5.033250 ms (FindLiveObjects: 0.310375 ms CreateObjectMapping: 0.142625 ms MarkObjects: 3.526708 ms  DeleteObjects: 1.053417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.352 seconds
Refreshing native plugins compatible for Editor in 0.22 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.494 seconds
Domain Reload Profiling: 848ms
	BeginReloadAssembly (97ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (11ms)
	LoadAllAssembliesAndSetupDomain (214ms)
		LoadAssemblies (136ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (103ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (495ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (381ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (251ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.32 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.4 MB). Loaded Objects now: 6794.
Memory consumption went from 160.2 MB to 156.7 MB.
Total: 5.075666 ms (FindLiveObjects: 0.292083 ms CreateObjectMapping: 0.102875 ms MarkObjects: 3.466583 ms  DeleteObjects: 1.213916 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.352 seconds
Refreshing native plugins compatible for Editor in 0.24 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.500 seconds
Domain Reload Profiling: 855ms
	BeginReloadAssembly (99ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (11ms)
	LoadAllAssembliesAndSetupDomain (214ms)
		LoadAssemblies (135ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (104ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (501ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (386ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (255ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.33 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.6 MB). Loaded Objects now: 6797.
Memory consumption went from 160.2 MB to 156.6 MB.
Total: 5.391750 ms (FindLiveObjects: 0.333250 ms CreateObjectMapping: 0.137166 ms MarkObjects: 3.773417 ms  DeleteObjects: 1.147666 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.352 seconds
Refreshing native plugins compatible for Editor in 0.21 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.488 seconds
Domain Reload Profiling: 842ms
	BeginReloadAssembly (99ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (43ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (11ms)
	LoadAllAssembliesAndSetupDomain (213ms)
		LoadAssemblies (136ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (103ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (489ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (376ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (246ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.34 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.5 MB). Loaded Objects now: 6800.
Memory consumption went from 160.2 MB to 156.7 MB.
Total: 5.098458 ms (FindLiveObjects: 0.289125 ms CreateObjectMapping: 0.119042 ms MarkObjects: 3.667375 ms  DeleteObjects: 1.022667 ms)

Prepare: number of updated asset objects reloaded= 1
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.361 seconds
Refreshing native plugins compatible for Editor in 0.22 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.500 seconds
Domain Reload Profiling: 863ms
	BeginReloadAssembly (104ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (11ms)
	LoadAllAssembliesAndSetupDomain (217ms)
		LoadAssemblies (137ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (500ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (388ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (82ms)
			ProcessInitializeOnLoadAttributes (257ms)
			ProcessInitializeOnLoadMethodAttributes (31ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.20 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.5 MB). Loaded Objects now: 6803.
Memory consumption went from 160.2 MB to 156.7 MB.
Total: 4.741833 ms (FindLiveObjects: 0.261416 ms CreateObjectMapping: 0.122000 ms MarkObjects: 3.230875 ms  DeleteObjects: 1.127459 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.363 seconds
Refreshing native plugins compatible for Editor in 0.23 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.499 seconds
Domain Reload Profiling: 864ms
	BeginReloadAssembly (103ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (219ms)
		LoadAssemblies (140ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (106ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (499ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (382ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (248ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.34 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.8 MB). Loaded Objects now: 6806.
Memory consumption went from 160.2 MB to 156.4 MB.
Total: 5.228500 ms (FindLiveObjects: 0.348291 ms CreateObjectMapping: 0.136459 ms MarkObjects: 3.582833 ms  DeleteObjects: 1.160625 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.366 seconds
Refreshing native plugins compatible for Editor in 0.23 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.502 seconds
Domain Reload Profiling: 871ms
	BeginReloadAssembly (105ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (220ms)
		LoadAssemblies (141ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (106ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (93ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (502ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (388ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (256ms)
			ProcessInitializeOnLoadMethodAttributes (31ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.31 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.6 MB). Loaded Objects now: 6809.
Memory consumption went from 160.2 MB to 156.6 MB.
Total: 5.004958 ms (FindLiveObjects: 0.291375 ms CreateObjectMapping: 0.131792 ms MarkObjects: 3.584500 ms  DeleteObjects: 0.996958 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.356 seconds
Refreshing native plugins compatible for Editor in 0.23 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.492 seconds
Domain Reload Profiling: 850ms
	BeginReloadAssembly (100ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (216ms)
		LoadAssemblies (136ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (492ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (377ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (84ms)
			ProcessInitializeOnLoadAttributes (245ms)
			ProcessInitializeOnLoadMethodAttributes (31ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (6ms)
Refreshing native plugins compatible for Editor in 0.27 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.7 MB). Loaded Objects now: 6812.
Memory consumption went from 160.2 MB to 156.5 MB.
Total: 4.929667 ms (FindLiveObjects: 0.261042 ms CreateObjectMapping: 0.123583 ms MarkObjects: 3.623334 ms  DeleteObjects: 0.921542 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 1456.274499 seconds.
  path: ProjectSettings/TagManager.asset
  artifactKey: Guid(00000000000000003000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing ProjectSettings/TagManager.asset using Guid(00000000000000003000000000000000) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'c1597cc740e5ee77d24112ab7909d92c') in 0.010148667 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.360 seconds
Refreshing native plugins compatible for Editor in 0.22 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.482 seconds
Domain Reload Profiling: 844ms
	BeginReloadAssembly (104ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (11ms)
	LoadAllAssembliesAndSetupDomain (215ms)
		LoadAssemblies (139ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (103ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (483ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (369ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (239ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.29 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6041 unused Assets / (3.7 MB). Loaded Objects now: 6815.
Memory consumption went from 160.2 MB to 156.5 MB.
Total: 4.827417 ms (FindLiveObjects: 0.288500 ms CreateObjectMapping: 0.138000 ms MarkObjects: 3.478792 ms  DeleteObjects: 0.921875 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.360 seconds
Refreshing native plugins compatible for Editor in 0.22 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.480 seconds
Domain Reload Profiling: 842ms
	BeginReloadAssembly (105ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (215ms)
		LoadAssemblies (136ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (480ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (366ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (238ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.35 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.4 MB). Loaded Objects now: 6818.
Memory consumption went from 160.1 MB to 156.7 MB.
Total: 5.009458 ms (FindLiveObjects: 0.299917 ms CreateObjectMapping: 0.123708 ms MarkObjects: 3.625875 ms  DeleteObjects: 0.959792 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.362 seconds
Refreshing native plugins compatible for Editor in 0.20 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.474 seconds
Domain Reload Profiling: 838ms
	BeginReloadAssembly (106ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (49ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (216ms)
		LoadAssemblies (138ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (474ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (361ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (234ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.34 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.5 MB). Loaded Objects now: 6821.
Memory consumption went from 160.1 MB to 156.6 MB.
Total: 5.123500 ms (FindLiveObjects: 0.302250 ms CreateObjectMapping: 0.120708 ms MarkObjects: 3.646292 ms  DeleteObjects: 1.052417 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.363 seconds
Refreshing native plugins compatible for Editor in 0.21 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.482 seconds
Domain Reload Profiling: 846ms
	BeginReloadAssembly (108ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (214ms)
		LoadAssemblies (138ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (103ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (482ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (370ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (242ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.30 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.5 MB). Loaded Objects now: 6824.
Memory consumption went from 160.1 MB to 156.6 MB.
Total: 4.906000 ms (FindLiveObjects: 0.287250 ms CreateObjectMapping: 0.126166 ms MarkObjects: 3.565000 ms  DeleteObjects: 0.927291 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.359 seconds
Refreshing native plugins compatible for Editor in 0.21 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.488 seconds
Domain Reload Profiling: 850ms
	BeginReloadAssembly (104ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (214ms)
		LoadAssemblies (137ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (104ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (489ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (376ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (247ms)
			ProcessInitializeOnLoadMethodAttributes (31ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.29 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.3 MB). Loaded Objects now: 6827.
Memory consumption went from 160.1 MB to 156.8 MB.
Total: 5.062833 ms (FindLiveObjects: 0.326625 ms CreateObjectMapping: 0.133208 ms MarkObjects: 3.563625 ms  DeleteObjects: 1.039250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.358 seconds
Refreshing native plugins compatible for Editor in 0.20 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.485 seconds
Domain Reload Profiling: 845ms
	BeginReloadAssembly (104ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (47ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (214ms)
		LoadAssemblies (137ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (103ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (91ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (485ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (372ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (243ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.26 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.4 MB). Loaded Objects now: 6830.
Memory consumption went from 160.1 MB to 156.7 MB.
Total: 5.257375 ms (FindLiveObjects: 0.299208 ms CreateObjectMapping: 0.124708 ms MarkObjects: 3.719584 ms  DeleteObjects: 1.113708 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.366 seconds
Refreshing native plugins compatible for Editor in 0.24 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.491 seconds
Domain Reload Profiling: 859ms
	BeginReloadAssembly (108ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (217ms)
		LoadAssemblies (138ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (93ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (491ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (377ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (83ms)
			ProcessInitializeOnLoadAttributes (246ms)
			ProcessInitializeOnLoadMethodAttributes (33ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.30 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.7 MB). Loaded Objects now: 6833.
Memory consumption went from 160.1 MB to 156.5 MB.
Total: 4.754416 ms (FindLiveObjects: 0.263417 ms CreateObjectMapping: 0.138833 ms MarkObjects: 3.429500 ms  DeleteObjects: 0.922375 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.351 seconds
Refreshing native plugins compatible for Editor in 0.21 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.484 seconds
Domain Reload Profiling: 837ms
	BeginReloadAssembly (98ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (42ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (11ms)
	LoadAllAssembliesAndSetupDomain (214ms)
		LoadAssemblies (135ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (103ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (90ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (484ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (370ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (242ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.31 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.6 MB). Loaded Objects now: 6836.
Memory consumption went from 160.1 MB to 156.5 MB.
Total: 4.934750 ms (FindLiveObjects: 0.290625 ms CreateObjectMapping: 0.126916 ms MarkObjects: 3.533917 ms  DeleteObjects: 0.983000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.360 seconds
Refreshing native plugins compatible for Editor in 0.21 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.476 seconds
Domain Reload Profiling: 838ms
	BeginReloadAssembly (102ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (46ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (11ms)
	LoadAllAssembliesAndSetupDomain (218ms)
		LoadAssemblies (139ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (104ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (477ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (364ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (80ms)
			ProcessInitializeOnLoadAttributes (236ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.30 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.4 MB). Loaded Objects now: 6839.
Memory consumption went from 160.1 MB to 156.8 MB.
Total: 4.915458 ms (FindLiveObjects: 0.312916 ms CreateObjectMapping: 0.115250 ms MarkObjects: 3.565458 ms  DeleteObjects: 0.921458 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.359 seconds
Refreshing native plugins compatible for Editor in 0.21 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.477 seconds
Domain Reload Profiling: 838ms
	BeginReloadAssembly (103ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (45ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (215ms)
		LoadAssemblies (139ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (103ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (90ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (477ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (363ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (11ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (237ms)
			ProcessInitializeOnLoadMethodAttributes (31ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.29 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.7 MB). Loaded Objects now: 6842.
Memory consumption went from 160.2 MB to 156.5 MB.
Total: 5.260000 ms (FindLiveObjects: 0.349375 ms CreateObjectMapping: 0.159166 ms MarkObjects: 3.620250 ms  DeleteObjects: 1.130959 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
Thread 0x17fe2b000 may have been prematurely finalized
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.363 seconds
Refreshing native plugins compatible for Editor in 0.22 ms, found 2 plugins.
Native extension for iOS target not found
Native extension for Android target not found
Native extension for OSXStandalone target not found
Native extension for WebGL target not found
[MCP Unity] Updated workspace configuration in /Users/<USER>/Documents/Myproject/Myproject.code-workspace
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:Log (object)
McpUnity.Utils.VsCodeWorkspaceUtils:AddPackageCacheToWorkspace () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs:66)
McpUnity.Unity.McpUnitySettings:.ctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:59)
McpUnity.Unity.McpUnitySettings:get_Instance () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnitySettings.cs:47)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:41)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/VsCodeWorkspaceUtils.cs Line: 66)

[MCP Unity] Failed to start WebSocket server: Address already in use
UnityEngine.Debug:ExtractStackTraceNoAlloc (byte*,int,string)
UnityEngine.StackTraceUtility:ExtractStackTrace () (at /Users/<USER>/build/output/unity/unity/Runtime/Export/Scripting/StackTrace.cs:37)
UnityEngine.DebugLogHandler:Internal_Log (UnityEngine.LogType,UnityEngine.LogOption,string,UnityEngine.Object)
UnityEngine.DebugLogHandler:LogFormat (UnityEngine.LogType,UnityEngine.Object,string,object[])
UnityEngine.Logger:Log (UnityEngine.LogType,object)
UnityEngine.Debug:LogError (object)
McpUnity.Utils.McpLogger:LogError (string) (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/Utils/Logger.cs:40)
McpUnity.Unity.McpUnityServer:StartServer () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:103)
McpUnity.Unity.McpUnityServer:.cctor () (at ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs:43)
System.Runtime.CompilerServices.RuntimeHelpers:RunClassConstructor (System.RuntimeTypeHandle)
UnityEditor.EditorAssemblies:ProcessInitializeOnLoadAttributes (System.Type[]) (at /Users/<USER>/build/output/unity/unity/Editor/Mono/EditorAssemblies.cs:118)

(Filename: ./Library/PackageCache/com.gamelovers.mcp-unity@4d0680b7541b/Editor/UnityBridge/McpUnityServer.cs Line: 103)

[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.476 seconds
Domain Reload Profiling: 840ms
	BeginReloadAssembly (103ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (4ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (44ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (12ms)
	LoadAllAssembliesAndSetupDomain (219ms)
		LoadAssemblies (141ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (105ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (92ms)
			ResolveRequiredComponents (6ms)
	FinalizeReload (476ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (363ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (12ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (235ms)
			ProcessInitializeOnLoadMethodAttributes (32ms)
			AfterProcessingInitializeOnLoad (1ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Refreshing native plugins compatible for Editor in 0.30 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 14 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6040 unused Assets / (3.3 MB). Loaded Objects now: 6845.
Memory consumption went from 160.2 MB to 156.9 MB.
Total: 4.924000 ms (FindLiveObjects: 0.270208 ms CreateObjectMapping: 0.122084 ms MarkObjects: 3.566250 ms  DeleteObjects: 0.965250 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 621.783399 seconds.
  path: Assets/Scripts/RoleControl1.cs
  artifactKey: Guid(29b9ba8bcda784e49b10e416cb67bea5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/RoleControl1.cs using Guid(29b9ba8bcda784e49b10e416cb67bea5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'abdff3f1d4decae4bb22a9f68c82c281') in 0.000805708 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

